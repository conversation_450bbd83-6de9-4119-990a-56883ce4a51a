export default defineNuxtConfig({
  modules: [
    '@nuxthub/core',
    '@nuxt/ui',
    '@nuxtjs/color-mode',
    '@compodium/nuxt',
  ],
  css: ['~/assets/css/main.css'],
  devtools: { enabled: true },
  app: {
    head: {
      link: [
        { rel: 'icon', type: 'image/svg+xml', href: '/favicon.svg' },
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
      ],
    },
  },
  runtimeConfig: {
    public: {
      helloText: 'Hello from the Edge 👋',
    },
  },
  future: { compatibilityVersion: 4 },
  compatibilityDate: '2025-05-15',
  hub: {},
});
