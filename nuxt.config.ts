export default defineNuxtConfig({
  modules: [
    '@nuxthub/core',
    '@nuxt/ui',
    '@nuxtjs/color-mode',
    '@compodium/nuxt',
  ],
  css: ['~/assets/css/main.css'],
  devtools: { enabled: true },

  // Nuxt UI configuration
  ui: {
    // Define which colors should be available for theming
    theme: {
      colors: [
        'primary',
        'secondary',
        'success',
        'info',
        'warning',
        'error',
        'neutral'
      ],
      // Enable transitions for smooth color changes
      transitions: true
    },
    // Component prefix (default is 'U')
    prefix: 'U',
    // Enable color mode integration
    colorMode: true
  },

  // Color mode configuration
  colorMode: {
    preference: 'system', // default value of $colorMode.preference
    fallback: 'light', // fallback value if not system preference found
    hid: 'nuxt-color-mode-script',
    globalName: '__NUXT_COLOR_MODE__',
    componentName: 'ColorScheme',
    classPrefix: '',
    classSuffix: '',
    storageKey: 'nuxt-color-mode'
  },

  app: {
    head: {
      link: [
        { rel: 'icon', type: 'image/svg+xml', href: '/favicon.svg' },
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        // Preload fonts for better performance
        { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
        { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: '' },
      ],
      meta: [
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'theme-color', content: '#ef4444' }, // Red primary color
        { name: 'color-scheme', content: 'light dark' }
      ]
    },
  },

  runtimeConfig: {
    public: {
      helloText: 'Hello from the Edge 👋',
    },
  },

  future: { compatibilityVersion: 4 },
  compatibilityDate: '2025-05-15',
  hub: {},
});
