# College Management System - Nuxt UI Page Prompts

## Overview
This document contains detailed prompts for creating each page/interface of the Advanced College Management System using Nuxt UI. Each prompt is designed to create minimal, elegant, and responsive interfaces that integrate seamlessly with the Taskmaster and Context7 AI features.

## Design Principles
- **Framework**: Nuxt 3 + Nuxt UI (50+ components)
- **Styling**: Tailwind CSS v4 with Nuxt UI theming
- **Responsiveness**: Mobile-first design
- **Accessibility**: WCAG AA compliance
- **Dark Mode**: Full support via @nuxtjs/color-mode
- **Icons**: Iconify integration

---

## 1. Authentication Pages

### 1.1 Login Page
```
Create a modern login page for a College Management System using Nuxt UI components. 

Requirements:
- Use UCard component as the main container
- UInput components for email/username and password fields
- UButton for login action with loading state
- UCheckbox for "Remember me" option
- ULink for "Forgot password" navigation
- UAlert for error messages
- Social login options using UButton with icons
- SSO integration buttons for institutional login
- UContainer for responsive layout
- Dark mode compatible
- Mobile-first responsive design
- Form validation with UFormGroup
- Clean, professional academic institution aesthetic
- Subtle college branding area
- UCard with subtle shadow and rounded corners
- Accessibility features with proper ARIA labels
```

### 1.2 Registration Page
```
Create a student/faculty registration page using Nuxt UI components.

Requirements:
- Multi-step registration wizard using UCard containers
- USteps component for progress indication
- UForm with comprehensive validation
- USelect for role selection (Student, Faculty, Staff)
- UInput components for personal information
- UTextarea for additional information
- URadioGroup for gender/preferences
- UFileInput for profile picture upload
- UButton for navigation (Previous, Next, Submit)
- UAlert for success/error messages
- Terms and conditions with UCheckbox
- Responsive grid layout using UContainer
- Form sections organized with UDivider
- Real-time validation feedback
- Dark mode support
- Professional academic styling
```

---

## 2. Dashboard Pages

### 2.1 Student Dashboard
```
Create a comprehensive student dashboard using Nuxt UI components.

Requirements:
- UContainer for main layout structure
- UCard components for different dashboard sections
- UAvatar with student profile picture
- UBadge for academic status indicators
- UProgress for GPA and credit progress
- UTable for current courses overview
- UButton for quick actions (Register, View Grades)
- UNotification area for important announcements
- UAlert for deadline reminders
- UChart components for academic progress visualization
- Quick access sidebar with UNavigation
- UDropdown for user menu
- UModal for quick task creation
- UCalendar component for academic calendar
- Statistics cards using UCard with UIcon
- Recent activity feed using UTimeline
- Responsive grid system for different screen sizes
- Integration points for AI chatbot (UChat interface)
- Taskmaster section with recent tasks preview
- Dark mode optimized color scheme
```

### 2.2 Faculty Dashboard
```
Create a faculty dashboard interface using Nuxt UI components.

Requirements:
- UContainer for responsive layout
- UCard sections for course management overview
- UTable for course listings with enrollment numbers
- UButton groups for quick course actions
- UBadge for course status indicators
- UCalendar for class schedules and office hours
- UChart for student performance analytics
- UNotification for pending grading tasks
- UAlert for important faculty announcements
- UDropdown for advanced course options
- UModal for quick grade entry
- Task management section using UCard
- AI-powered tools section with UButton
- UProgress bars for semester progress
- UNavigation for faculty-specific features
- UTimeline for recent faculty activities
- UAvatar with faculty profile
- Research and publication tracking cards
- Office hours management interface
- Responsive design for tablet and mobile use
- Integration with Context7 AI features
```

### 2.3 Admin Dashboard
```
Create an administrative dashboard using Nuxt UI components.

Requirements:
- UContainer with admin-specific layout
- UCard grid for key metrics and KPIs
- UTable for user management overview
- UChart components for institutional analytics
- UButton for system administration actions
- UBadge for system status indicators
- UAlert for system notifications
- UNavigation for admin module access
- UDropdown for bulk operations
- UModal for user creation/editing
- UProgress for system resource utilization
- UCalendar for institutional calendar
- UNotification for security alerts
- Financial overview cards with UIcon
- USelect for filtering and sorting options
- Task management dashboard integration
- Real-time statistics using UChart
- UTimeline for system activity logs
- User roles and permissions interface
- Context7 knowledge base management
- Dark mode with admin-focused styling
- Responsive design for large screens
```

---

## 3. Academic Management Pages

### 3.1 Course Catalog Page
```
Create a comprehensive course catalog interface using Nuxt UI components.

Requirements:
- UContainer for main layout
- UInput with UIcon for search functionality
- USelect for department/level filtering
- UCard for each course listing
- UBadge for course attributes (credits, level, prerequisites)
- UButton for course details and registration
- UPagination for course list navigation
- UTable for detailed course information view
- UModal for course detail popup
- UAccordion for course descriptions
- UCollapse for prerequisite information
- UChart for course capacity visualization
- UAlert for registration status messages
- UNavigation for department categories
- UDropdown for sort options
- UCheckbox for advanced filtering
- UDivider between course sections
- UIcon for course type indicators
- AI-powered course recommendation section
- Favorite courses functionality with UButton
- UCalendar for course schedule preview
- Responsive grid layout for course cards
- Integration with prerequisite validation system
- Dark mode compatible design
```

### 3.2 Course Registration Page
```
Create a course registration interface using Nuxt UI components.

Requirements:
- UContainer for layout structure
- USteps for registration process progression
- UTable for available courses with enrollment status
- UButton for add/drop course actions
- UBadge for course status (Available, Waitlist, Full)
- UAlert for registration conflicts and messages
- UCard for selected courses summary
- UCalendar for schedule visualization
- UProgress for credit hour tracking
- UModal for course conflict resolution
- UInput for course search within registration
- USelect for semester/term selection
- UCheckbox for course preferences
- UNotification for real-time updates
- UChart for degree progress visualization
- AI-powered schedule optimization suggestions
- UAccordion for course details during selection
- UDivider between registration sections
- Prerequisites validation with UAlert
- UTimeline for registration deadline tracking
- Responsive design for mobile registration
- Integration with academic advisor system
- Dark mode support
```

### 3.3 Grade Management Page (Faculty)
```
Create a grade management interface for faculty using Nuxt UI components.

Requirements:
- UContainer for main layout
- USelect for course and section selection
- UTable for student roster with grade columns
- UInput for grade entry with validation
- UButton for grade calculation and submission
- UModal for individual student grade details
- UAlert for grading deadline notifications
- UCard for grading statistics overview
- UProgress for grading completion status
- UChart for grade distribution visualization
- UDropdown for grading scale options
- UBadge for assignment status indicators
- UCalendar for assignment due dates
- UAccordion for assignment categories
- UTextarea for grade comments
- UNotification for grade submission confirmations
- AI-powered grading assistance integration
- UTimeline for grading history
- UNavigation for different assignment types
- Bulk grading operations interface
- UDivider between grade sections
- Export functionality with UButton
- Responsive design for tablet use
- Dark mode optimized for extended use
```

---

## 4. Student Information System

### 4.1 Student Profile Page
```
Create a comprehensive student profile interface using Nuxt UI components.

Requirements:
- UContainer for profile layout
- UCard for personal information section
- UAvatar for profile picture with edit capability
- UForm for profile information editing
- UInput components for personal details
- USelect for demographic information
- UTextarea for bio/additional information
- UButton for profile update actions
- UBadge for academic status indicators
- UTable for academic history
- UChart for GPA trends over time
- UProgress for degree completion status
- UAccordion for contact information sections
- UAlert for profile completion reminders
- UModal for emergency contact editing
- UTimeline for academic milestones
- UNavigation for profile sections
- UDivider between information groups
- UIcon for contact method indicators
- Document upload section with UFileInput
- Privacy settings with UCheckbox
- UCalendar for important personal dates
- Responsive design for mobile editing
- Dark mode support for accessibility
```

### 4.2 Academic Transcript Page
```
Create an academic transcript interface using Nuxt UI components.

Requirements:
- UContainer for transcript layout
- UCard for semester/term sections
- UTable for course and grade listings
- UBadge for course type indicators (Core, Elective)
- UProgress for cumulative GPA tracking
- UChart for GPA trend visualization
- UButton for transcript download/print
- USelect for term filtering
- UAlert for transcript notes and disclaimers
- UAccordion for detailed semester breakdowns
- UDivider between academic years
- UIcon for grade symbols and legends
- UModal for course detail viewing
- UNavigation for quick term jumping
- UDropdown for transcript format options
- Statistics summary using UCard
- UTimeline for academic progression
- Honors and awards section with UBadge
- Transfer credit visualization
- UCalendar for graduation timeline
- Official transcript request interface
- Responsive design for document viewing
- Print-optimized styling options
```

---

## 5. Financial Management Pages

### 5.1 Tuition & Fees Page
```
Create a tuition and fees management interface using Nuxt UI components.

Requirements:
- UContainer for financial overview layout
- UCard for billing summary sections
- UTable for detailed fee breakdowns
- UBadge for payment status indicators
- UButton for payment actions and options
- UAlert for payment deadlines and notices
- UProgress for payment plan completion
- UChart for payment history visualization
- UModal for payment method management
- USelect for payment plan options
- UInput for payment amount entry
- UCalendar for payment due dates
- UAccordion for fee category details
- UDivider between billing periods
- UIcon for payment method indicators
- UNotification for payment confirmations
- UTimeline for payment history
- UNavigation for different fee categories
- UDropdown for semester selection
- Financial aid integration display
- Late fee calculation interface
- UTextarea for payment notes
- Responsive design for mobile payments
- Secure payment form styling
```

### 5.2 Financial Aid Page
```
Create a financial aid management interface using Nuxt UI components.

Requirements:
- UContainer for aid overview layout
- UCard for different aid type sections
- UTable for scholarship and grant listings
- UBadge for aid status indicators
- UButton for application actions
- UProgress for application completion status
- UAlert for aid deadlines and requirements
- UChart for aid distribution visualization
- UModal for aid application details
- UForm for aid application submission
- USelect for aid type filtering
- UInput for application information
- UCalendar for aid deadlines
- UAccordion for aid requirement details
- UTimeline for application process tracking
- UNavigation for aid categories
- UDivider between aid sections
- UIcon for aid type indicators
- Document upload interface with UFileInput
- Eligibility checker with UCheckbox
- UNotification for application updates
- AI-powered aid recommendation system
- Responsive design for application forms
- Dark mode support for form filling
```

---

## 6. Taskmaster Integration Pages

### 6.1 Project Dashboard
```
Create a Taskmaster project dashboard using Nuxt UI components.

Requirements:
- UContainer for project overview layout
- UCard for individual project cards
- UButton for project creation and actions
- UBadge for project status and priority indicators
- UProgress for project completion tracking
- UTable for project task listings
- UChart for project analytics and timelines
- UModal for project creation/editing
- UForm for project information entry
- USelect for project categorization
- UInput for project search and filtering
- UCalendar for project deadlines
- UAccordion for project details expansion
- UTimeline for project milestone tracking
- UNavigation for project categories
- UAlert for project deadline notifications
- UDropdown for project sorting options
- UDivider between project sections
- UIcon for project type indicators
- Collaborative features with UAvatar
- AI-powered task expansion suggestions
- UTextarea for project descriptions
- Responsive grid layout for project cards
- Integration with college data linking
```

### 6.2 Task Management Page
```
Create a comprehensive task management interface using Nuxt UI components.

Requirements:
- UContainer for task management layout
- UCard for task grouping and organization
- UTable for detailed task listings
- UButton for task actions (Create, Edit, Complete)
- UBadge for task priority and status
- UProgress for task completion tracking
- UModal for task creation and editing
- UForm for task information input
- USelect for task assignment and categorization
- UInput for task search and filtering
- UCalendar for deadline management
- UAccordion for task detail expansion
- UTimeline for task activity history
- UAlert for overdue task notifications
- UNavigation for task view switching
- UDropdown for bulk task operations
- UDivider between task sections
- UIcon for task type and priority indicators
- UTextarea for task descriptions and notes
- File attachment interface with UFileInput
- AI-powered task research integration
- Collaborative commenting system
- UCheckbox for task completion status
- Responsive design for mobile task management
```

---

## 7. AI-Powered Features

### 7.1 AI Chatbot/Helpdesk Interface
```
Create an AI-powered helpdesk chat interface using Nuxt UI components.

Requirements:
- UContainer for chat layout
- UCard for chat message container
- UInput for message composition
- UButton for message sending and actions
- UAvatar for AI assistant and user identification
- UBadge for message status and type indicators
- UAlert for AI disclaimers and source citations
- UModal for expanded information viewing
- UAccordion for FAQ sections
- UNavigation for chat categories and history
- UTimeline for conversation flow
- UDivider between conversation sections
- UIcon for message type indicators
- UDropdown for chat preferences
- UTextarea for longer message composition
- UProgress for AI response loading
- UNotification for chat notifications
- Context7 source attribution display
- USelect for chat topic selection
- Quick action buttons with UButton
- Responsive design for mobile chat
- Dark mode optimized for readability
- Accessibility features for screen readers
- Integration with college knowledge base
```

### 7.2 AI Content Creation Interface
```
Create an AI-assisted content creation interface using Nuxt UI components.

Requirements:
- UContainer for content creation layout
- UCard for content sections and tools
- UTextarea for content input and editing
- UButton for AI generation and editing actions
- USelect for content type and templates
- UInput for content parameters and prompts
- UProgress for content generation status
- UAlert for AI generation disclaimers
- UModal for content preview and editing
- UAccordion for content structure organization
- UNavigation for content categories
- UDropdown for formatting options
- UDivider between content sections
- UIcon for content type indicators
- UBadge for content status and quality
- UTimeline for content revision history
- Context7 source validation display
- UForm for content metadata
- UCalendar for content scheduling
- Export options with UButton
- Collaborative editing features
- UNotification for generation completion
- Responsive design for content creation
- Integration with course management system
```

---

## 8. Communication & Collaboration

### 8.1 Messaging System
```
Create a secure messaging system using Nuxt UI components.

Requirements:
- UContainer for messaging layout
- UCard for conversation containers
- UInput for message composition
- UButton for sending and message actions
- UAvatar for user identification
- UBadge for message status and notifications
- UNavigation for contact lists and groups
- UModal for contact information and settings
- UAccordion for conversation groups
- UTimeline for message chronology
- UAlert for system messages and notifications
- UDropdown for message options and settings
- UDivider between conversation sections
- UIcon for message type and status indicators
- UTextarea for longer message composition
- UFileInput for attachment sharing
- USelect for recipient selection
- UProgress for file upload status
- UNotification for new message alerts
- Search functionality with UInput
- UCalendar for scheduled messages
- Message encryption indicators
- Responsive design for mobile messaging
- Dark mode support for extended use
```

### 8.2 Announcements Page
```
Create a college-wide announcements interface using Nuxt UI components.

Requirements:
- UContainer for announcements layout
- UCard for individual announcement display
- UButton for announcement actions and interactions
- UBadge for announcement priority and category
- UAlert for urgent announcements
- UNavigation for announcement categories
- UModal for announcement details and creation
- UForm for announcement composition
- USelect for target audience selection
- UInput for announcement search and filtering
- UCalendar for announcement scheduling
- UAccordion for announcement archives
- UTimeline for announcement chronology
- UDropdown for announcement management
- UDivider between announcement sections
- UIcon for announcement type indicators
- UTextarea for announcement content
- UProgress for announcement reach tracking
- UNotification for new announcements
- Attachment support with UFileInput
- UAvatar for announcement author display
- Comments and reactions interface
- Responsive design for announcement viewing
- Integration with notification system
```

---

## 9. Analytics & Reporting

### 9.1 Academic Analytics Dashboard
```
Create an academic analytics dashboard using Nuxt UI components.

Requirements:
- UContainer for analytics layout
- UCard for metric display sections
- UChart for various academic visualizations
- UButton for report generation and export
- USelect for data filtering and time periods
- UTable for detailed academic data
- UBadge for performance indicators
- UProgress for goal tracking displays
- UModal for detailed metric viewing
- UNavigation for analytics categories
- UAccordion for expandable data sections
- UAlert for data insights and recommendations
- UDropdown for chart type selection
- UDivider between analytics sections
- UIcon for metric type indicators
- UCalendar for date range selection
- UInput for custom metric searching
- UTimeline for trend analysis
- Real-time data indicators with UNotification
- Export functionality with UButton
- Comparative analysis tools
- Responsive design for data visualization
- Dark mode optimized for data reading
- Integration with AI-powered insights
```

### 9.2 Custom Reports Interface
```
Create a custom report generation interface using Nuxt UI components.

Requirements:
- UContainer for report builder layout
- UCard for report configuration sections
- UForm for report parameter setup
- USelect for data source and field selection
- UButton for report generation and actions
- UTable for report preview and results
- UChart for visual report elements
- UModal for report template management
- UAccordion for advanced report options
- UNavigation for report categories
- UInput for report naming and searching
- UCalendar for date range specification
- UProgress for report generation status
- UAlert for report validation messages
- UDropdown for output format selection
- UDivider between report sections
- UIcon for report type indicators
- UTextarea for report descriptions
- UBadge for report status indicators
- Scheduling interface with UCalendar
- UNotification for report completion
- Template library with UCard
- Responsive design for report building
- Export and sharing functionality
```

---

## 10. System Administration

### 10.1 User Management Interface
```
Create a comprehensive user management interface using Nuxt UI components.

Requirements:
- UContainer for user management layout
- UTable for user listings with pagination
- UButton for user actions (Create, Edit, Delete, Activate)
- UModal for user creation and editing forms
- UForm for user information input
- USelect for role and permission assignment
- UInput for user search and filtering
- UBadge for user status and role indicators
- UAlert for user management notifications
- UNavigation for user categories and groups
- UAccordion for user detail expansion
- UDropdown for bulk user operations
- UProgress for user onboarding status
- UDivider between user sections
- UIcon for user type and status indicators
- UAvatar for user profile display
- UCalendar for user activity tracking
- UTextarea for user notes and comments
- Password management with UInput
- Permission matrix with UCheckbox
- UNotification for user status changes
- Audit trail with UTimeline
- Responsive design for administration
- Integration with authentication system
```

### 10.2 System Settings Page
```
Create a system settings and configuration interface using Nuxt UI components.

Requirements:
- UContainer for settings layout
- UCard for settings category organization
- UForm for configuration input
- USelect for option selection and dropdowns
- UInput for parameter configuration
- UButton for settings save and reset actions
- USwitch for boolean configuration options
- UAlert for settings change notifications
- UModal for advanced configuration dialogs
- UAccordion for settings group organization
- UNavigation for settings categories
- UProgress for configuration validation
- UDropdown for preset configuration options
- UDivider between settings sections
- UIcon for settings type indicators
- UTextarea for configuration descriptions
- UBadge for setting status indicators
- UCalendar for scheduled maintenance
- Backup and restore interface
- Security settings with UCheckbox
- UNotification for configuration updates
- Context7 knowledge base configuration
- Responsive design for system administration
- Integration with Cloudflare services
```

---

## Implementation Guidelines

### Component Usage Best Practices
- Always use UContainer for main layout structure
- Implement proper form validation with UForm and UFormGroup
- Use UAlert for user feedback and error messages
- Implement loading states with UButton loading props
- Use UBadge consistently for status indicators
- Implement UModal for detailed views and forms
- Use UNavigation for consistent site navigation
- Implement UNotification for system messages

### Responsive Design Principles
- Mobile-first approach using Nuxt UI's responsive utilities
- Consistent spacing using Nuxt UI's spacing system
- Proper grid layouts using UContainer and flexbox utilities
- Touch-friendly interface elements for mobile devices

### Accessibility Requirements
- Proper ARIA labels for all interactive elements
- Keyboard navigation support
- Screen reader compatibility
- High contrast support for visually impaired users
- Focus management for modal and dropdown interactions

### Performance Considerations
- Lazy loading for data-heavy components
- Efficient state management for real-time updates
- Optimized chart rendering for analytics pages
- Pagination for large data sets

### Integration Points
- Taskmaster context linking throughout the system
- Context7 AI integration for accurate information display
- Real-time notifications and updates
- Seamless navigation between related features
```
