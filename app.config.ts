export default defineAppConfig({
  ui: {
    // Color system configuration
    colors: {
      primary: 'red',
      secondary: 'blue',
      neutral: 'slate',
      success: 'green',
      warning: 'amber',
      error: 'red',
      info: 'sky'
    },

    // Global component theme overrides
    button: {
      slots: {
        base: 'font-medium transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2'
      },
      defaultVariants: {
        size: 'md',
        color: 'primary',
        variant: 'solid'
      },
      variants: {
        size: {
          xs: {
            base: 'px-2 py-1 text-xs'
          },
          sm: {
            base: 'px-3 py-1.5 text-sm'
          },
          md: {
            base: 'px-4 py-2 text-sm'
          },
          lg: {
            base: 'px-6 py-3 text-base'
          },
          xl: {
            base: 'px-8 py-4 text-lg'
          }
        }
      }
    },

    // Card component theming
    card: {
      slots: {
        root: 'bg-default ring-1 ring-default divide-y divide-default rounded-xl shadow-sm',
        header: 'p-6',
        body: 'p-6',
        footer: 'p-6'
      }
    },

    // Input component theming
    input: {
      slots: {
        base: 'block w-full rounded-lg border-0 py-2 px-3 text-default bg-default ring-1 ring-inset ring-default placeholder:text-muted focus:ring-2 focus:ring-inset focus:ring-primary transition-colors duration-200'
      }
    },

    // Avatar component theming
    avatar: {
      slots: {
        root: 'inline-flex items-center justify-center shrink-0 select-none overflow-hidden rounded-full align-middle bg-elevated ring-1 ring-default',
        image: 'h-full w-full rounded-[inherit] object-cover'
      },
      variants: {
        size: {
          xs: { root: 'size-6 text-xs' },
          sm: { root: 'size-8 text-sm' },
          md: { root: 'size-10 text-base' },
          lg: { root: 'size-12 text-lg' },
          xl: { root: 'size-16 text-xl' }
        }
      },
      defaultVariants: {
        size: 'md'
      }
    }
  }
});
