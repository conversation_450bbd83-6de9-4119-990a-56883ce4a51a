@import "tailwindcss";

/* College Management System Custom Theme */
@theme static {
  /* Custom Fonts */
  --font-sans: 'Inter', ui-sans-serif, system-ui, sans-serif;

  /* Custom Breakpoints */
  --breakpoint-3xl: 1920px;

  /* College Brand Colors */
  --color-college-blue-50: #eff6ff;
  --color-college-blue-100: #dbeafe;
  --color-college-blue-200: #bfdbfe;
  --color-college-blue-300: #93c5fd;
  --color-college-blue-400: #60a5fa;
  --color-college-blue-500: #3b82f6;
  --color-college-blue-600: #2563eb;
  --color-college-blue-700: #1d4ed8;
  --color-college-blue-800: #1e40af;
  --color-college-blue-900: #1e3a8a;
  --color-college-blue-950: #172554;

  --color-college-accent-50: #fef7ee;
  --color-college-accent-100: #fdedd3;
  --color-college-accent-200: #fbd7a5;
  --color-college-accent-300: #f8bb6d;
  --color-college-accent-400: #f59532;
  --color-college-accent-500: #f37316;
  --color-college-accent-600: #e4570c;
  --color-college-accent-700: #bd420c;
  --color-college-accent-800: #973511;
  --color-college-accent-900: #7a2e11;
  --color-college-accent-950: #431506;
}

/* College Management System Custom Styles */

/* Custom CSS Variables for College Branding */
:root {
  --college-primary: #2563eb;
  --college-primary-dark: #1d4ed8;
  --college-secondary: #64748b;
  --college-accent: #f37316;
  --college-success: #10b981;
  --college-warning: #f59e0b;
  --college-error: #ef4444;
  
  /* Typography */
  --college-font-family: 'Inter', ui-sans-serif, system-ui, sans-serif;
  --college-font-size-xs: 0.75rem;
  --college-font-size-sm: 0.875rem;
  --college-font-size-base: 1rem;
  --college-font-size-lg: 1.125rem;
  --college-font-size-xl: 1.25rem;
  --college-font-size-2xl: 1.5rem;
  --college-font-size-3xl: 1.875rem;
  --college-font-size-4xl: 2.25rem;
  
  /* Spacing */
  --college-spacing-xs: 0.5rem;
  --college-spacing-sm: 0.75rem;
  --college-spacing-md: 1rem;
  --college-spacing-lg: 1.5rem;
  --college-spacing-xl: 2rem;
  --college-spacing-2xl: 3rem;
  
  /* Border Radius */
  --college-radius-sm: 0.375rem;
  --college-radius-md: 0.5rem;
  --college-radius-lg: 0.75rem;
  --college-radius-xl: 1rem;
  
  /* Shadows */
  --college-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --college-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --college-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Dark mode variables */
.dark {
  --college-primary: #3b82f6;
  --college-primary-dark: #2563eb;
  --college-secondary: #94a3b8;
}

/* Base Typography */
body {
  font-family: var(--font-sans);
  line-height: 1.6;
  color: rgb(17 24 39);
}

.dark body {
  color: rgb(243 244 246);
}

/* Custom Component Classes */
.college-card {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm ring-1 ring-gray-200 dark:ring-gray-700;
}

.college-button-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium px-4 py-2.5 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800;
}

.college-button-secondary {
  @apply bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 font-medium px-4 py-2.5 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800;
}

.college-input {
  @apply block w-full rounded-lg border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400 text-sm;
}

.college-badge-success {
  @apply inline-flex items-center rounded-full bg-green-100 dark:bg-green-900/20 px-2.5 py-1 text-xs font-medium text-green-800 dark:text-green-400;
}

.college-badge-warning {
  @apply inline-flex items-center rounded-full bg-yellow-100 dark:bg-yellow-900/20 px-2.5 py-1 text-xs font-medium text-yellow-800 dark:text-yellow-400;
}

.college-badge-error {
  @apply inline-flex items-center rounded-full bg-red-100 dark:bg-red-900/20 px-2.5 py-1 text-xs font-medium text-red-800 dark:text-red-400;
}

.college-badge-info {
  @apply inline-flex items-center rounded-full bg-blue-100 dark:bg-blue-900/20 px-2.5 py-1 text-xs font-medium text-blue-800 dark:text-blue-400;
}

/* Navigation Styles */
.college-nav-link {
  @apply text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200;
}

.college-nav-link-active {
  @apply bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400 px-3 py-2 rounded-md text-sm font-medium;
}

/* Table Styles */
.college-table {
  @apply min-w-full divide-y divide-gray-200 dark:divide-gray-700;
}

.college-table-header {
  @apply bg-gray-50 dark:bg-gray-800;
}

.college-table-header-cell {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider;
}

.college-table-body {
  @apply bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700;
}

.college-table-cell {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100;
}

/* Form Styles */
.college-form-group {
  @apply space-y-1;
}

.college-form-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.college-form-help {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

.college-form-error {
  @apply text-sm text-red-600 dark:text-red-400;
}

/* Status Indicators */
.status-active {
  @apply text-green-800 dark:text-green-400 bg-green-100 dark:bg-green-900/20;
}

.status-inactive {
  @apply text-red-800 dark:text-red-400 bg-red-100 dark:bg-red-900/20;
}

.status-pending {
  @apply text-yellow-800 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20;
}

.status-enrolled {
  @apply text-blue-800 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/20;
}

.status-graduated {
  @apply text-purple-800 dark:text-purple-400 bg-purple-100 dark:bg-purple-900/20;
}

/* Utility Classes */
.college-shadow {
  box-shadow: var(--college-shadow-md);
}

.college-shadow-lg {
  box-shadow: var(--college-shadow-lg);
}

/* Responsive Design Helpers */
.college-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.college-section {
  @apply py-8 sm:py-12 lg:py-16;
}

/* Animation Classes */
.college-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.college-slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus States for Accessibility */
.college-focus {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800;
}

/* Print Styles */
@media print {
  .college-no-print {
    display: none !important;
  }
  
  .college-print-only {
    display: block !important;
  }
}
