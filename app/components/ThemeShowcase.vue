<template>
  <div class="space-y-8 p-6">
    <!-- Theme Controls -->
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="text-xl font-semibold text-highlighted">Theme Configuration</h2>
          <UButton
            :icon="isDark ? 'i-lucide-moon' : 'i-lucide-sun'"
            color="neutral"
            variant="ghost"
            @click="toggleColorMode"
          />
        </div>
      </template>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Color Mode -->
        <div>
          <label class="block text-sm font-medium text-toned mb-2">Color Mode</label>
          <URadioGroup
            v-model="colorMode.preference"
            :items="[
              { label: 'Light', value: 'light' },
              { label: 'Dark', value: 'dark' },
              { label: 'System', value: 'system' }
            ]"
            color="primary"
          />
        </div>
        
        <!-- Theme Presets -->
        <div>
          <label class="block text-sm font-medium text-toned mb-2">Theme Preset</label>
          <USelect
            v-model="selectedPreset"
            :options="presetOptions"
            @change="applyThemePreset"
          />
        </div>
        
        <!-- Primary Color -->
        <div>
          <label class="block text-sm font-medium text-toned mb-2">Primary Color</label>
          <div class="grid grid-cols-4 gap-2">
            <button
              v-for="(color, key) in colorPalettes"
              :key="key"
              :class="[
                'w-8 h-8 rounded-full border-2 transition-all',
                `bg-${color}-500`,
                selectedPrimaryColor === color 
                  ? 'border-default ring-2 ring-primary' 
                  : 'border-muted hover:border-default'
              ]"
              @click="updatePrimaryColor(color)"
            />
          </div>
        </div>
      </div>
    </UCard>

    <!-- Typography Showcase -->
    <UCard>
      <template #header>
        <h3 class="text-lg font-semibold text-highlighted">Typography</h3>
      </template>
      
      <div class="space-y-4">
        <div>
          <h1 class="text-4xl font-bold text-highlighted">Heading 1</h1>
          <h2 class="text-3xl font-semibold text-highlighted">Heading 2</h2>
          <h3 class="text-2xl font-medium text-highlighted">Heading 3</h3>
          <h4 class="text-xl font-medium text-default">Heading 4</h4>
          <h5 class="text-lg font-medium text-default">Heading 5</h5>
          <h6 class="text-base font-medium text-toned">Heading 6</h6>
        </div>
        
        <div class="space-y-2">
          <p class="text-highlighted">Highlighted text for important content</p>
          <p class="text-default">Default text for regular content</p>
          <p class="text-toned">Toned text for secondary content</p>
          <p class="text-muted">Muted text for less important content</p>
          <p class="text-dimmed">Dimmed text for subtle content</p>
        </div>
      </div>
    </UCard>

    <!-- Component Showcase -->
    <UCard>
      <template #header>
        <h3 class="text-lg font-semibold text-highlighted">Components</h3>
      </template>
      
      <div class="space-y-6">
        <!-- Buttons -->
        <div>
          <h4 class="text-sm font-medium text-toned mb-3">Buttons</h4>
          <div class="flex flex-wrap gap-3">
            <UButton color="primary">Primary</UButton>
            <UButton color="secondary">Secondary</UButton>
            <UButton color="success">Success</UButton>
            <UButton color="warning">Warning</UButton>
            <UButton color="error">Error</UButton>
            <UButton color="neutral" variant="outline">Neutral</UButton>
          </div>
        </div>
        
        <!-- Inputs -->
        <div>
          <h4 class="text-sm font-medium text-toned mb-3">Form Elements</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UInput placeholder="Enter your name" />
            <USelect 
              :options="['Option 1', 'Option 2', 'Option 3']" 
              placeholder="Select an option"
            />
            <UTextarea placeholder="Enter your message" />
            <UCheckbox label="I agree to the terms" />
          </div>
        </div>
        
        <!-- Badges and Chips -->
        <div>
          <h4 class="text-sm font-medium text-toned mb-3">Badges & Status</h4>
          <div class="flex flex-wrap gap-3">
            <UBadge color="primary">Primary</UBadge>
            <UBadge color="success">Success</UBadge>
            <UBadge color="warning">Warning</UBadge>
            <UBadge color="error">Error</UBadge>
            <UBadge color="neutral" variant="subtle">Neutral</UBadge>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Color Palette -->
    <UCard>
      <template #header>
        <h3 class="text-lg font-semibold text-highlighted">Color Palette</h3>
      </template>
      
      <div class="space-y-4">
        <div v-for="(colorName, key) in ['primary', 'secondary', 'success', 'warning', 'error', 'neutral']" :key="key">
          <h5 class="text-sm font-medium text-toned mb-2 capitalize">{{ colorName }}</h5>
          <div class="flex gap-1">
            <div
              v-for="shade in [50, 100, 200, 300, 400, 500, 600, 700, 800, 900]"
              :key="shade"
              :class="[
                'w-12 h-12 rounded flex items-center justify-center text-xs font-medium',
                `bg-${colorName}-${shade}`,
                shade >= 500 ? 'text-white' : 'text-gray-900'
              ]"
            >
              {{ shade }}
            </div>
          </div>
        </div>
      </div>
    </UCard>
  </div>
</template>

<script setup lang="ts">
const { 
  colorMode, 
  isDark, 
  toggleColorMode, 
  colorPalettes, 
  themePresets,
  applyThemePreset: applyPreset 
} = useTheme()

const selectedPreset = ref<keyof typeof themePresets>('default')
const selectedPrimaryColor = ref('red')

const presetOptions = computed(() => 
  Object.keys(themePresets).map(key => ({
    label: key.charAt(0).toUpperCase() + key.slice(1),
    value: key
  }))
)

const applyThemePreset = (preset: keyof typeof themePresets) => {
  selectedPreset.value = preset
  applyPreset(preset)
}

const updatePrimaryColor = (color: string) => {
  selectedPrimaryColor.value = color
  // This would update the app configuration in a real implementation
  console.log('Updating primary color to:', color)
}
</script>
