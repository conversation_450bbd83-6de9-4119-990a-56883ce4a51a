<script setup lang="ts">
// Test page for Nuxt UI components
const isLoading = ref(false)
const selectedTab = ref('overview')

const testNotification = () => {
  // This will be implemented when we add toast notifications
  console.log('Test notification clicked')
}

const testAction = async () => {
  isLoading.value = true
  await new Promise(resolve => setTimeout(resolve, 2000))
  isLoading.value = false
}
</script>

<template>
  <div class="space-y-8">
    <!-- Welcome Section -->
    <div class="text-center">
      <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
        College Management System
      </h1>
      <p class="text-lg text-gray-600 dark:text-gray-300 mb-8">
        Welcome to the advanced college management platform
      </p>
    </div>

    <!-- Component Testing Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- Buttons Card -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">Buttons</h3>
        </template>

        <div class="space-y-3">
          <UButton color="primary" block>Primary Button</UButton>
          <UButton color="secondary" variant="outline" block>Secondary Button</UButton>
          <UButton color="gray" variant="soft" block>Soft Button</UButton>
          <UButton
            color="primary"
            :loading="isLoading"
            @click="testAction"
            block
          >
            Test Loading
          </UButton>
        </div>
      </UCard>

      <!-- Form Elements Card -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">Form Elements</h3>
        </template>

        <div class="space-y-3">
          <UInput placeholder="Enter your name" />
          <UTextarea placeholder="Enter description" />
          <USelect
            :options="[
              { label: 'Student', value: 'student' },
              { label: 'Faculty', value: 'faculty' },
              { label: 'Admin', value: 'admin' }
            ]"
            placeholder="Select role"
          />
          <UCheckbox label="Enable notifications" />
        </div>
      </UCard>

      <!-- Alerts Card -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">Alerts</h3>
        </template>

        <div class="space-y-3">
          <UAlert
            icon="i-heroicons-information-circle"
            color="blue"
            variant="soft"
            title="Information"
            description="This is an informational alert."
          />
          <UAlert
            icon="i-heroicons-check-circle"
            color="green"
            variant="soft"
            title="Success"
            description="Operation completed successfully."
          />
          <UAlert
            icon="i-heroicons-exclamation-triangle"
            color="yellow"
            variant="soft"
            title="Warning"
            description="Please review this information."
          />
        </div>
      </UCard>
    </div>

    <!-- Tabs Section -->
    <UCard>
      <template #header>
        <h3 class="text-lg font-semibold">Navigation Tabs</h3>
      </template>

      <UTabs
        :items="[
          { key: 'overview', label: 'Overview' },
          { key: 'students', label: 'Students' },
          { key: 'courses', label: 'Courses' },
          { key: 'reports', label: 'Reports' }
        ]"
        v-model="selectedTab"
      >
        <template #overview>
          <div class="p-4">
            <h4 class="font-medium mb-2">System Overview</h4>
            <p class="text-gray-600 dark:text-gray-300">
              This is the main dashboard where you can view system statistics and recent activities.
            </p>
          </div>
        </template>

        <template #students>
          <div class="p-4">
            <h4 class="font-medium mb-2">Student Management</h4>
            <p class="text-gray-600 dark:text-gray-300">
              Manage student profiles, enrollment, and academic records.
            </p>
          </div>
        </template>

        <template #courses>
          <div class="p-4">
            <h4 class="font-medium mb-2">Course Management</h4>
            <p class="text-gray-600 dark:text-gray-300">
              Create and manage courses, schedules, and curriculum.
            </p>
          </div>
        </template>

        <template #reports>
          <div class="p-4">
            <h4 class="font-medium mb-2">Reports & Analytics</h4>
            <p class="text-gray-600 dark:text-gray-300">
              Generate reports and view analytics for institutional insights.
            </p>
          </div>
        </template>
      </UTabs>
    </UCard>

    <!-- Badge and Avatar Section -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">Badges & Status</h3>
        </template>

        <div class="flex flex-wrap gap-2">
          <UBadge color="green" variant="soft">Active</UBadge>
          <UBadge color="yellow" variant="soft">Pending</UBadge>
          <UBadge color="red" variant="soft">Inactive</UBadge>
          <UBadge color="blue" variant="soft">Enrolled</UBadge>
          <UBadge color="purple" variant="soft">Graduate</UBadge>
        </div>
      </UCard>

      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">User Avatars</h3>
        </template>

        <div class="flex items-center space-x-3">
          <UAvatar src="https://avatars.githubusercontent.com/u/739984?v=4" alt="User 1" />
          <UAvatar src="https://avatars.githubusercontent.com/u/904724?v=4" alt="User 2" />
          <UAvatar text="JD" alt="John Doe" />
          <UAvatar text="SM" alt="Sarah Miller" />
        </div>
      </UCard>
    </div>
  </div>
</template>
