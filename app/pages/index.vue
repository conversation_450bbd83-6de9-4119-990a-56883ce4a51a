<script setup lang="ts">
// Enhanced page showcasing the comprehensive theme system
const { colorMode, isDark, toggleColorMode } = useTheme()
const isLoading = ref(false)
const selectedTab = ref('theme')

const testNotification = () => {
  // This will be implemented when we add toast notifications
  console.log('Test notification clicked')
}

const testAction = async () => {
  isLoading.value = true
  await new Promise(resolve => setTimeout(resolve, 2000))
  isLoading.value = false
}

// Page metadata
useSeoMeta({
  title: 'College Management System - Theme Showcase',
  description: 'Comprehensive theme system built with Nuxt UI and Tailwind CSS v4'
})
</script>

<template>
  <div class="min-h-screen bg-default">
    <!-- Header with theme toggle -->
    <header class="sticky top-0 z-50 bg-default/80 backdrop-blur-sm border-b border-default">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center space-x-4">
            <h1 class="text-xl font-bold text-highlighted">College Management System</h1>
            <UBadge color="primary" variant="subtle">Theme Showcase</UBadge>
          </div>

          <div class="flex items-center space-x-4">
            <UButton
              :icon="isDark ? 'i-lucide-moon' : 'i-lucide-sun'"
              color="neutral"
              variant="ghost"
              size="sm"
              @click="toggleColorMode"
            />
            <UBadge :color="isDark ? 'neutral' : 'primary'" variant="outline">
              {{ isDark ? 'Dark' : 'Light' }} Mode
            </UBadge>
          </div>
        </div>
      </div>
    </header>

    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Welcome Section with enhanced styling -->
      <div class="text-center mb-12">
        <h2 class="text-4xl font-bold text-highlighted mb-4 text-balance">
          Comprehensive Theme System
        </h2>
        <p class="text-lg text-muted mb-8 max-w-2xl mx-auto text-pretty">
          Experience our advanced theming system built with Nuxt UI v3 and Tailwind CSS v4,
          featuring dynamic color modes, responsive design tokens, and comprehensive component styling.
        </p>

        <div class="flex items-center justify-center space-x-4">
          <UButton color="primary" size="lg" icon="i-lucide-palette">
            Explore Themes
          </UButton>
          <UButton color="neutral" variant="outline" size="lg" icon="i-lucide-settings">
            Customize
          </UButton>
        </div>
      </div>

      <!-- Main Content Tabs -->
      <UTabs
        :items="[
          { key: 'theme', label: 'Theme System', icon: 'i-lucide-palette' },
          { key: 'components', label: 'Components', icon: 'i-lucide-box' },
          { key: 'overview', label: 'System Overview', icon: 'i-lucide-layout-dashboard' },
          { key: 'documentation', label: 'Documentation', icon: 'i-lucide-book-open' }
        ]"
        v-model="selectedTab"
        class="w-full"
      >
        <!-- Theme System Tab -->
        <template #theme>
          <div class="mt-6">
            <ThemeShowcase />
          </div>
        </template>

        <!-- Components Tab -->
        <template #components>
          <div class="mt-6 space-y-8">
            <!-- Component Testing Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <!-- Enhanced Buttons Card -->
              <UCard>
                <template #header>
                  <div class="flex items-center space-x-2">
                    <UIcon name="i-lucide-mouse-pointer-click" class="text-primary" />
                    <h3 class="text-lg font-semibold text-highlighted">Buttons</h3>
                  </div>
                </template>

                <div class="space-y-3">
                  <UButton color="primary" block>Primary Button</UButton>
                  <UButton color="secondary" variant="outline" block>Secondary Button</UButton>
                  <UButton color="neutral" variant="subtle" block>Neutral Button</UButton>
                  <UButton color="success" variant="soft" block>Success Button</UButton>
                  <UButton
                    color="primary"
                    :loading="isLoading"
                    @click="testAction"
                    block
                  >
                    Test Loading
                  </UButton>
                </div>
              </UCard>

              <!-- Enhanced Form Elements Card -->
              <UCard>
                <template #header>
                  <div class="flex items-center space-x-2">
                    <UIcon name="i-lucide-form-input" class="text-primary" />
                    <h3 class="text-lg font-semibold text-highlighted">Form Elements</h3>
                  </div>
                </template>

                <div class="space-y-3">
                  <UInput placeholder="Enter your name" />
                  <UTextarea placeholder="Enter description" />
                  <USelect
                    :options="[
                      { label: 'Student', value: 'student' },
                      { label: 'Faculty', value: 'faculty' },
                      { label: 'Admin', value: 'admin' }
                    ]"
                    placeholder="Select role"
                  />
                  <UCheckbox label="Enable notifications" />
                  <URadioGroup
                    :items="[
                      { label: 'Active', value: 'active' },
                      { label: 'Inactive', value: 'inactive' }
                    ]"
                    color="primary"
                  />
                </div>
              </UCard>

              <!-- Enhanced Alerts Card -->
              <UCard>
                <template #header>
                  <div class="flex items-center space-x-2">
                    <UIcon name="i-lucide-alert-circle" class="text-primary" />
                    <h3 class="text-lg font-semibold text-highlighted">Alerts & Status</h3>
                  </div>
                </template>

                <div class="space-y-3">
                  <UAlert
                    icon="i-lucide-info"
                    color="info"
                    variant="subtle"
                    title="Information"
                    description="This is an informational alert using our theme system."
                  />
                  <UAlert
                    icon="i-lucide-check-circle"
                    color="success"
                    variant="subtle"
                    title="Success"
                    description="Operation completed successfully with enhanced styling."
                  />
                  <UAlert
                    icon="i-lucide-triangle-alert"
                    color="warning"
                    variant="subtle"
                    title="Warning"
                    description="Please review this information carefully."
                  />
                  <UAlert
                    icon="i-lucide-x-circle"
                    color="error"
                    variant="subtle"
                    title="Error"
                    description="An error occurred during processing."
                  />
                </div>
              </UCard>
            </div>

            <!-- Additional Component Showcase -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Badges & Avatars -->
              <UCard>
                <template #header>
                  <div class="flex items-center space-x-2">
                    <UIcon name="i-lucide-user-circle" class="text-primary" />
                    <h3 class="text-lg font-semibold text-highlighted">Badges & Avatars</h3>
                  </div>
                </template>

                <div class="space-y-4">
                  <div>
                    <h4 class="text-sm font-medium text-toned mb-2">Status Badges</h4>
                    <div class="flex flex-wrap gap-2">
                      <UBadge color="success" variant="subtle">Active</UBadge>
                      <UBadge color="warning" variant="subtle">Pending</UBadge>
                      <UBadge color="error" variant="subtle">Inactive</UBadge>
                      <UBadge color="info" variant="subtle">Enrolled</UBadge>
                      <UBadge color="secondary" variant="subtle">Graduate</UBadge>
                    </div>
                  </div>

                  <div>
                    <h4 class="text-sm font-medium text-toned mb-2">User Avatars</h4>
                    <div class="flex items-center space-x-3">
                      <UAvatar src="https://avatars.githubusercontent.com/u/739984?v=4" alt="User 1" size="sm" />
                      <UAvatar src="https://avatars.githubusercontent.com/u/904724?v=4" alt="User 2" />
                      <UAvatar text="JD" alt="John Doe" size="lg" />
                      <UAvatar text="SM" alt="Sarah Miller" size="xl" />
                    </div>
                  </div>
                </div>
              </UCard>

              <!-- Navigation & Controls -->
              <UCard>
                <template #header>
                  <div class="flex items-center space-x-2">
                    <UIcon name="i-lucide-navigation" class="text-primary" />
                    <h3 class="text-lg font-semibold text-highlighted">Navigation</h3>
                  </div>
                </template>

                <div class="space-y-4">
                  <div>
                    <h4 class="text-sm font-medium text-toned mb-2">Pagination</h4>
                    <UPagination :page="3" :total="100" color="primary" />
                  </div>

                  <div>
                    <h4 class="text-sm font-medium text-toned mb-2">Progress</h4>
                    <UProgress :value="65" color="primary" class="mb-2" />
                    <UProgress :value="40" color="success" class="mb-2" />
                    <UProgress :value="80" color="warning" />
                  </div>
                </div>
              </UCard>
            </div>
          </div>
        </template>

        <!-- System Overview Tab -->
        <template #overview>
          <div class="mt-6 space-y-6">
            <!-- System Stats -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <UCard>
                <div class="flex items-center space-x-3">
                  <div class="p-2 bg-primary/10 rounded-lg">
                    <UIcon name="i-lucide-users" class="text-primary text-xl" />
                  </div>
                  <div>
                    <p class="text-2xl font-bold text-highlighted">1,234</p>
                    <p class="text-sm text-muted">Total Students</p>
                  </div>
                </div>
              </UCard>

              <UCard>
                <div class="flex items-center space-x-3">
                  <div class="p-2 bg-success/10 rounded-lg">
                    <UIcon name="i-lucide-book-open" class="text-success text-xl" />
                  </div>
                  <div>
                    <p class="text-2xl font-bold text-highlighted">89</p>
                    <p class="text-sm text-muted">Active Courses</p>
                  </div>
                </div>
              </UCard>

              <UCard>
                <div class="flex items-center space-x-3">
                  <div class="p-2 bg-warning/10 rounded-lg">
                    <UIcon name="i-lucide-user-check" class="text-warning text-xl" />
                  </div>
                  <div>
                    <p class="text-2xl font-bold text-highlighted">156</p>
                    <p class="text-sm text-muted">Faculty Members</p>
                  </div>
                </div>
              </UCard>

              <UCard>
                <div class="flex items-center space-x-3">
                  <div class="p-2 bg-info/10 rounded-lg">
                    <UIcon name="i-lucide-graduation-cap" class="text-info text-xl" />
                  </div>
                  <div>
                    <p class="text-2xl font-bold text-highlighted">342</p>
                    <p class="text-sm text-muted">Graduates</p>
                  </div>
                </div>
              </UCard>
            </div>

            <!-- Recent Activities -->
            <UCard>
              <template #header>
                <div class="flex items-center space-x-2">
                  <UIcon name="i-lucide-activity" class="text-primary" />
                  <h3 class="text-lg font-semibold text-highlighted">Recent Activities</h3>
                </div>
              </template>

              <div class="space-y-4">
                <div class="flex items-center space-x-3 p-3 bg-muted rounded-lg">
                  <UAvatar text="JS" size="sm" />
                  <div class="flex-1">
                    <p class="text-sm font-medium text-default">John Smith enrolled in Computer Science</p>
                    <p class="text-xs text-muted">2 hours ago</p>
                  </div>
                  <UBadge color="success" variant="subtle">New</UBadge>
                </div>

                <div class="flex items-center space-x-3 p-3 bg-muted rounded-lg">
                  <UAvatar text="MD" size="sm" />
                  <div class="flex-1">
                    <p class="text-sm font-medium text-default">Dr. Maria Davis created new course</p>
                    <p class="text-xs text-muted">4 hours ago</p>
                  </div>
                  <UBadge color="info" variant="subtle">Course</UBadge>
                </div>

                <div class="flex items-center space-x-3 p-3 bg-muted rounded-lg">
                  <UAvatar text="RJ" size="sm" />
                  <div class="flex-1">
                    <p class="text-sm font-medium text-default">Robert Johnson submitted assignment</p>
                    <p class="text-xs text-muted">6 hours ago</p>
                  </div>
                  <UBadge color="warning" variant="subtle">Assignment</UBadge>
                </div>
              </div>
            </UCard>
          </div>
        </template>

        <!-- Documentation Tab -->
        <template #documentation>
          <div class="mt-6">
            <UCard>
              <template #header>
                <div class="flex items-center space-x-2">
                  <UIcon name="i-lucide-book-open" class="text-primary" />
                  <h3 class="text-lg font-semibold text-highlighted">Theme System Documentation</h3>
                </div>
              </template>

              <div class="prose prose-slate dark:prose-invert max-w-none">
                <h4>Comprehensive Theme Configuration</h4>
                <p class="text-muted">
                  Our theme system is built on top of Nuxt UI v3 and Tailwind CSS v4, providing a robust foundation for consistent design across the application.
                </p>

                <h5>Key Features:</h5>
                <ul class="text-muted">
                  <li><strong>Dynamic Color System:</strong> Runtime color configuration with support for primary, secondary, and semantic colors</li>
                  <li><strong>Dark/Light Mode:</strong> Automatic theme switching with system preference detection</li>
                  <li><strong>Component Theming:</strong> Comprehensive component styling with variant support</li>
                  <li><strong>Design Tokens:</strong> Consistent spacing, typography, and layout tokens</li>
                  <li><strong>Responsive Design:</strong> Mobile-first approach with custom breakpoints</li>
                </ul>

                <h5>Configuration Files:</h5>
                <ul class="text-muted">
                  <li><code>app.config.ts</code> - Runtime theme configuration and component overrides</li>
                  <li><code>nuxt.config.ts</code> - Module configuration and color mode settings</li>
                  <li><code>assets/css/main.css</code> - CSS variables and custom theme definitions</li>
                  <li><code>composables/useTheme.ts</code> - Theme utilities and helper functions</li>
                </ul>

                <div class="mt-6 p-4 bg-muted rounded-lg">
                  <h6 class="text-highlighted font-medium mb-2">Quick Start:</h6>
                  <p class="text-sm text-muted">
                    The theme system is automatically configured and ready to use. Switch between light and dark modes using the toggle button in the header,
                    or explore the Theme System tab to see all available customization options.
                  </p>
                </div>
              </div>
            </UCard>
          </div>
        </template>
      </UTabs>
    </main>
  </div>
</template>
