/**
 * Theme composable for managing application theming
 * Provides utilities for working with Nuxt UI theme system
 */

export interface ThemeColors {
  primary: string
  secondary: string
  neutral: string
  success: string
  warning: string
  error: string
  info: string
}

export interface ThemeConfig {
  colors: ThemeColors
  radius: string
  container: string
  transitions: boolean
}

/**
 * Available color palettes for the theme system
 */
export const colorPalettes = {
  red: 'red',
  blue: 'blue', 
  green: 'green',
  amber: 'amber',
  purple: 'purple',
  pink: 'pink',
  indigo: 'indigo',
  cyan: 'cyan',
  teal: 'teal',
  lime: 'lime',
  orange: 'orange',
  slate: 'slate',
  gray: 'gray',
  zinc: 'zinc',
  neutral: 'neutral',
  stone: 'stone'
} as const

export type ColorPalette = keyof typeof colorPalettes

/**
 * Predefined theme configurations
 */
export const themePresets = {
  default: {
    colors: {
      primary: 'red',
      secondary: 'blue',
      neutral: 'slate',
      success: 'green',
      warning: 'amber', 
      error: 'red',
      info: 'sky'
    },
    radius: '0.5rem',
    container: 'var(--container-7xl)',
    transitions: true
  },
  minimal: {
    colors: {
      primary: 'slate',
      secondary: 'gray',
      neutral: 'zinc',
      success: 'green',
      warning: 'amber',
      error: 'red',
      info: 'blue'
    },
    radius: '0.25rem',
    container: 'var(--container-6xl)',
    transitions: false
  },
  vibrant: {
    colors: {
      primary: 'purple',
      secondary: 'pink',
      neutral: 'slate',
      success: 'emerald',
      warning: 'orange',
      error: 'red',
      info: 'cyan'
    },
    radius: '0.75rem',
    container: 'var(--container-8xl)',
    transitions: true
  }
} as const

export type ThemePreset = keyof typeof themePresets

/**
 * Main theme composable
 */
export const useTheme = () => {
  const colorMode = useColorMode()
  
  /**
   * Get current theme configuration
   */
  const getCurrentTheme = (): ThemeConfig => {
    return themePresets.default
  }
  
  /**
   * Apply a theme preset
   */
  const applyThemePreset = (preset: ThemePreset) => {
    const config = themePresets[preset]
    // This would typically update the app.config.ts or runtime configuration
    console.log('Applying theme preset:', preset, config)
  }
  
  /**
   * Update specific color in theme
   */
  const updateThemeColor = (colorType: keyof ThemeColors, palette: ColorPalette) => {
    console.log(`Updating ${colorType} to ${palette}`)
    // Implementation would update the runtime configuration
  }
  
  /**
   * Toggle between light and dark mode
   */
  const toggleColorMode = () => {
    colorMode.preference = colorMode.value === 'dark' ? 'light' : 'dark'
  }
  
  /**
   * Check if current mode is dark
   */
  const isDark = computed(() => colorMode.value === 'dark')
  
  /**
   * Get CSS variable value for current theme
   */
  const getCSSVariable = (variable: string) => {
    if (process.client) {
      return getComputedStyle(document.documentElement).getPropertyValue(variable)
    }
    return ''
  }
  
  /**
   * Set CSS variable value
   */
  const setCSSVariable = (variable: string, value: string) => {
    if (process.client) {
      document.documentElement.style.setProperty(variable, value)
    }
  }
  
  return {
    // State
    colorMode,
    isDark,
    
    // Methods
    getCurrentTheme,
    applyThemePreset,
    updateThemeColor,
    toggleColorMode,
    getCSSVariable,
    setCSSVariable,
    
    // Constants
    colorPalettes,
    themePresets
  }
}

/**
 * Utility function to generate component theme classes
 */
export const generateThemeClasses = (component: string, variant?: string, color?: string) => {
  const baseClasses = []
  
  if (color) {
    baseClasses.push(`text-${color}`)
    baseClasses.push(`border-${color}`)
  }
  
  if (variant) {
    baseClasses.push(`${component}-${variant}`)
  }
  
  return baseClasses.join(' ')
}

/**
 * Responsive design tokens
 */
export const responsiveTokens = {
  breakpoints: {
    xs: '475px',
    sm: '640px', 
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
    '3xl': '1920px',
    '4xl': '2560px'
  },
  containers: {
    xs: '20rem',
    sm: '24rem',
    md: '28rem', 
    lg: '32rem',
    xl: '36rem',
    '2xl': '42rem',
    '3xl': '48rem',
    '4xl': '56rem',
    '5xl': '64rem',
    '6xl': '72rem',
    '7xl': '80rem',
    '8xl': '90rem'
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
    '3xl': '4rem'
  }
}
